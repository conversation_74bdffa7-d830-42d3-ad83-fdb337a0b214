package executor

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"sync"
	"time"
)

type IExecutor interface {
	// 判断执行器是否可以执行
	CanExecute() bool                               // 执行器是否可以执行
	Execute() (bool, string)                        // 执行函数
	ExecuteWithRetry(maxRetries int) (bool, string) // 带重试的执行函数

	// 记录执行器执行状态
	Record() (bool, string)                      // 记录状态
	UpdateStatus(status int, reason string) bool // 更新状态
	Notify(failedReason string) bool             // 通知状态

	// 回调
	HandleCallback(interface{}) error // 回调函数

	// 初始化执行器
	Init() bool
	RunOne()
	GenName() string
}

var ExecutorManager *IExecutorManager

type IExecutorManager struct {
	ExecutorInfo sync.Map // 使用sync.Map替代普通map，支持并发安全
	// 添加清理机制相关字段
	lastCleanup time.Time
	cleanupMu   sync.Mutex
}

func NewExecutorManager() {
	ExecutorManager = &IExecutorManager{
		lastCleanup: time.Now(),
	}
	// 启动定期清理goroutine
	go ExecutorManager.startCleanupRoutine()
}

func (t *IExecutorManager) Run() {
	plog.EngineLG.Info("executor manager engine run")
	for task := range comm.ScalerCh.ExecutorChan {
		go func(task *comm.ModuleOneTaskParam) {
			executor := NewExecutor(*task)
			if executor == nil {
				plog.EngineLG.Error("failed to create executor")
				return
			}
			if !executor.Init() {
				plog.EngineLG.Error("failed to init executor")
				return
			}
			name := executor.GenName()
			t.ExecutorInfo.Store(name, executor)
			executor.RunOne()
			// 执行完成后清理
			t.ExecutorInfo.Delete(name)
		}(task)
	}
}

func NewExecutor(p comm.ModuleOneTaskParam) IExecutor {
	switch p.ModuleInfo.ScaleEngine {
	case base.ENGINE_NAME_SERVICE_CAPACITY:
		return &ExecutorService{
			ModuleOneTaskParam: p,
		}
	case base.ENGINE_NAME_MODEL:
		return &ExecutorModel{
			ModuleOneTaskParam: p,
		}
	}
	return nil
}

// startCleanupRoutine 启动定期清理routine
func (t *IExecutorManager) startCleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		t.cleanup()
	}
}

// cleanup 清理过期的执行器
func (t *IExecutorManager) cleanup() {
	t.cleanupMu.Lock()
	defer t.cleanupMu.Unlock()

	now := time.Now()
	if now.Sub(t.lastCleanup) < time.Minute {
		return // 避免频繁清理
	}

	count := 0
	t.ExecutorInfo.Range(func(key, value interface{}) bool {
		count++
		return true
	})

	plog.EngineLG.Debugf("ExecutorManager cleanup: current active executors: %d", count)
	t.lastCleanup = now
}

// GetActiveCount 获取当前活跃的执行器数量
func (t *IExecutorManager) GetActiveCount() int {
	count := 0
	t.ExecutorInfo.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// 初始化管理器（在init函数或main中调用）
func init() {
	NewExecutorManager()
}
