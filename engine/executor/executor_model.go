package executor

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/service_model"
	"encoding/json"
	"fmt"
	"time"
)

type ExecutorModel struct {
	comm.ModuleOneTaskParam
	EngineData dao.EngineInfo
}

var _ IExecutor = &ExecutorModel{}

func (e *ExecutorModel) GenName() string {
	// 任务类型_模块名_模块ID_任务ID_规则ID
	return fmt.Sprintf("%s_%d_%d_%d", e.TaskInfo.TaskType, e.ModuleInfo.ID, e.TaskInfo.ID, e.RuleInfo.ID)
}

func (e *ExecutorModel) CanExecute() (canRun bool) {
	// 检查资源是否可用、依赖是否满足等(待todo)
	// 查询弹性伸缩记录，如果存在运行任务且运行时间小于1天，则不执行当前任务，发送通告提醒
	dataList, r := dao.CreateEngineInfoPtr().GetEngineInfoByStatus(e.TaskInfo.IdcTag, []int{base.ENGINE_TASK_STATUS_RUNNING})
	if !r.IsOk() {
		plog.EngineLG.Errorf("query engine info failed, err: %v", r)
		return
	}
	for _, d := range dataList {
		if time.Since(d.CreateTime).Hours() < 24 {
			e.Notify(NOTICE_ERROR_SAME_TAG_RUNNING)
			plog.EngineLG.Warnf("engine task is running")
			return false
		}
	}
	return true
}

func (e *ExecutorModel) Execute() (bool, string) {
	request, response, err := service_model.ModelEngineExecute(e.ModuleInfo.Name, e.EngineInfo.Action, e.EngineInfo.ExpectedNum)
	requestJson, _ := json.Marshal(&request)
	responseJson, _ := json.Marshal(&response)

	dao.CreateEngineInfoPtr().UpdateByPk(&dao.EngineInfo{
		ScaleId: response.Data.TaskId, ResponseInfo: string(responseJson), RequestInfo: string(requestJson)},
		[]string{"ScaleId", "ResponseInfo", "RequestInfo"}, e.EngineInfo.ID)

	if err != nil {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to execute engine: %v", err)
		return false, NOTICE_ERROR_REQUEST_FAILED
	}

	if response.ResCode != "0" {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to execute engine, res_code: %s, res_msg: %s ", response.ResCode, response.ResMsg)
		return false, NOTICE_ERROR_REQUEST_FAILED
	}

	return true, ""
}

func (e *ExecutorModel) ExecuteWithRetry(maxRetries int) (success bool, failedReason string) {
	for i := 0; i < maxRetries; i++ {
		if success, failedReason = e.Execute(); success {
			// 执行成功后，开始监控实例数变更
			if e.monitorInstanceCountChange() {
				return true, ""
			} else {
				return false, NOTICE_ERROR_INSTANCE_COUNT_NOT_MATCH
			}
		}
	}
	return false, NOTICE_ERROR_REQUEST_FAILED
}

func (e *ExecutorModel) Record() (bool, string) {
	var (
		finalNumInfo dao.ProcessInfo
		r            ocommon.ResultInfo
		engineId     int
	)
	err := json.Unmarshal([]byte(e.DecisionInfo.ProcessInfo), &finalNumInfo)
	if finalNumInfo.FinalNum == 0 || err != nil {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to parse decision info: %v", err)
		return false, NOTICE_ERROR_GET_DECISION_DATA
	}

	e.EngineInfo = dao.EngineInfo{
		ModuleId:       e.ModuleInfo.ID,
		ServiceName:    e.ModuleInfo.Name,
		TaskId:         e.TaskInfo.ID,
		RuleId:         e.RuleInfo.ID,
		IdcTag:         e.TaskInfo.IdcTag,
		TaskType:       e.TaskInfo.TaskType,
		TaskStatus:     e.TaskInfo.TaskStatus,
		ScaleEngine:    e.ModuleInfo.ModuleType,
		Action:         e.DecisionInfo.Action,
		Concurrency:    1,
		AdjustmentType: e.TaskInfo.SchedMode,
		AdjustNum:      e.DecisionInfo.AdjustmentValue,
		CurrentNum:     e.DecisionInfo.CurrentNum,
		ExpectedNum:    finalNumInfo.FinalNum,
		Status:         base.ENGINE_TASK_STATUS_RUNNING,
		CreateTime:     time.Now(),
		UpdateTime:     time.Now(),
		FinishTime:     global.ZeroTime,
		LastModifyTime: time.Now(),
	}
	engineId, r = dao.CreateEngineInfoPtr().Insert(&e.EngineInfo)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to insert engine info: %v", r)
		return false, NOTICE_ERROR_REQUEST_DATABASE
	}
	e.EngineInfo.ID = engineId
	dao.CreateDecisionRecordPtr().UpdateByPk(&dao.DecisionRecord{EngineId: engineId}, []string{"EngineId"}, e.DecisionInfo.ID)
	return true, ""
}

func (e *ExecutorModel) UpdateStatus(status int, reason string) bool {
	var (
		r ocommon.ResultInfo
	)
	e.EngineInfo.Status = status
	_, r = dao.CreateEngineInfoPtr().UpdateByPk(
		&dao.EngineInfo{Status: status, UpdateTime: time.Now(), FinishTime: time.Now(), FailureType: reason},
		[]string{"Status", "FailureType", "UpdateTime", "FinishTime"}, e.EngineInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to update engine status: %v", r)
		return false
	}
	_, r = dao.CreateDecisionRecordPtr().UpdateByColumn(
		&dao.DecisionRecord{Status: status}, []string{"Status"},
		&dao.DecisionRecord{EngineId: e.EngineInfo.ID}, []string{"EngineId"})

	if e.TaskInfo.TaskType == base.TASK_TYPE_MANUAL {
		switch status {
		case base.ENGINE_TASK_STATUS_SUCCESS:
			dao.CreateTaskInfoPtr().UpdateTaskStatus(e.TaskInfo.ID, base.TASK_MANUAL_STATUS_SUCCESS)
		case base.ENGINE_TASK_STATUS_FAILED:
			dao.CreateTaskInfoPtr().UpdateTaskStatus(e.TaskInfo.ID, base.TASK_MANUAL_STATUS_FAILED)
		}
	}
	return true
}

func (e *ExecutorModel) Notify(string) bool {
	e.ModuleOneTaskParam.Notify()
	return true
}

func (e *ExecutorModel) Init() bool {
	e.Phase = comm.EngineStartPhase
	return true
}

func (e *ExecutorModel) RunOne() {
	var (
		success    bool
		execResult string
	)

	if e.CanExecute() {
		if success, execResult = e.Record(); !success {
			e.UpdateStatus(base.ENGINE_TASK_STATUS_FAILED, execResult)
			plog.EngineLG.WithFields(e.Fields()).Error("record failed")
		}
		if success, execResult = e.ExecuteWithRetry(1); success {
			plog.EngineLG.WithFields(e.Fields()).Debugf("execution succeeded")
		} else {
			e.UpdateStatus(base.ENGINE_TASK_STATUS_FAILED, execResult)
			plog.EngineLG.WithFields(e.Fields()).Error("execution failed after retries")
		}
		e.Notify(execResult)
	} else {
		e.Notify(NOTICE_ERROR_SAME_TAG_RUNNING)
		plog.EngineLG.WithFields(e.Fields()).Warn("execution not allowed")
	}
}

func (e *ExecutorModel) HandleCallback(input interface{}) error {
	return nil
}

func (e *ExecutorModel) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   e.ModuleInfo.ID,
		"module_name": e.ModuleInfo.Name,
		"task_id":     e.TaskInfo.ID,
		"rule_id":     e.RuleInfo.ID,
		"trace_id":    e.TraceID,
	}
}

// monitorInstanceCountChange 监控实例数变更，每分钟检查一次，共检查30分钟
func (e *ExecutorModel) monitorInstanceCountChange() bool {
	expectedInstanceCount := e.EngineInfo.ExpectedNum
	serviceName := e.ModuleInfo.ServiceName
	idcTag := e.TaskInfo.IdcTag

	plog.EngineLG.WithFields(e.Fields()).Infof("start monitoring instance count change, expected: %d, service: %s, idc: %s",
		expectedInstanceCount, serviceName, idcTag)

	// 检查30次，每次间隔1分钟
	for i := 0; i < 30; i++ {
		// 等待1分钟
		time.Sleep(1 * time.Minute)

		// 查询monitor_info表获取当前实例数
		currentInstanceCount, err := e.getCurrentInstanceCount(serviceName, idcTag)
		if err != nil {
			plog.EngineLG.WithFields(e.Fields()).Errorf("failed to get current instance count, attempt %d/30, err: %v", i+1, err)
			continue
		}

		plog.EngineLG.WithFields(e.Fields()).Infof("instance count check attempt %d/30, current: %d, expected: %d",
			i+1, currentInstanceCount, expectedInstanceCount)

		// 如果达到预期实例数，认为成功
		if currentInstanceCount == expectedInstanceCount {
			plog.EngineLG.WithFields(e.Fields()).Infof("instance count reached expected value after %d minutes, current: %d, expected: %d",
				i+1, currentInstanceCount, expectedInstanceCount)
			return true
		}
	}

	// 30分钟后仍未达到预期，认为失败
	plog.EngineLG.WithFields(e.Fields()).Errorf("instance count monitoring timeout after 30 minutes, expected: %d", expectedInstanceCount)
	return false
}

// getCurrentInstanceCount 从monitor_info表获取当前实例数
func (e *ExecutorModel) getCurrentInstanceCount(serviceName, idcTag string) (int, error) {
	// 获取最近的监控数据
	startTime := time.Now().Add(-5 * time.Minute).Format("2006-01-02 15:04:05")
	endTime := time.Now().Format("2006-01-02 15:04:05")

	monitorList, err := dao.CreateMonitorInfoPtr().GetLatestBetweenTime(serviceName, idcTag, startTime, endTime)
	if err != nil {
		return 0, fmt.Errorf("failed to get monitor info: %v", err)
	}

	if len(monitorList) == 0 {
		return 0, fmt.Errorf("no monitor data found for service %s, idc %s", serviceName, idcTag)
	}

	// 返回最新的实例数
	return monitorList[0].InsCount, nil
}
