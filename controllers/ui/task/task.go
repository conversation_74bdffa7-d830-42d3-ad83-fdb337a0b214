package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/errno"
	modelComm "dxm/siod_sre/auto-scaler/models/comm"
	"dxm/siod_sre/auto-scaler/models/task"
	"time"

	"dxm/siod-cloud/go-common-lib/otool"

	"github.com/astaxie/beego"
)

type TaskController struct {
	beego.Controller
}

type taskQueryInput struct {
	task.TaskQuery
}

func (c *TaskController) GetTaskList() {
	var input taskQueryInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input.TaskQuery, &c.Controller)
	if r.Is<PERSON>k() {
		r = task.GetTaskList(input.TaskQuery)
	}

	r.Dump("")
	c.<PERSON>["json"] = r
	c.ServeJSON()
}

func (c *TaskController) GetTaskHistory() {
	var input taskQueryInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input.TaskQuery, &c.Controller)
	if r.IsOk() {
		r = task.GetTaskHistory(input.TaskQuery)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) GetManualRule() {
	var input taskQueryInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input.TaskQuery, &c.Controller)
	if r.IsOk() {
		r = task.GetManualRule(input.TaskQuery)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) GetCrontabRule() {
	var input taskQueryInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input.TaskQuery, &c.Controller)
	if r.IsOk() {
		r = task.GetCrontabRule(input.TaskQuery)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) GetAutoTaskRule() {
	var input taskQueryInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input.TaskQuery, &c.Controller)
	if r.IsOk() {
		r = task.GetAutoTaskRule(input.TaskQuery)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) ManualTaskRun() {
	var (
		input    taskQueryInput
		r        ocommon.ResultInfo
		ruleInfo dao.RuleOnline
	)
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input.TaskQuery, &c.Controller)
	}
	if r.IsOk() {
		r = dao.CreateRuleOnlinePtr().SearchByColumn(&ruleInfo, &dao.RuleOnline{TaskId: input.TaskId}, []string{"TaskId"})
		if !r.IsOk() {
			olog.Error("failed to get rule info when manual task run, taskId:[%d], err:[%v]", input.TaskId, r)
		}
	}

	// 获取任务信息并更新实例数
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "manual_task_run", input)
		r = updateRuleWithCurrentInstanceCount(input.TaskId, &ruleInfo)
		if !r.IsOk() {
			olog.Error("failed to update rule with current instance count, taskId:[%d], err:[%v]", input.TaskId, r)
		}
	}

	if r.IsOk() {
		go func() {
			plog.EngineLG.Infof("manual task run, taskId:[%d], ruleInfo:[%v]", input.TaskId, ruleInfo)
			comm.ScalerCh.EventOtherChan <- ruleInfo
		}()
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) ManualTaskCreate() {
	var input task.ManualTaskCreateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "manual_task_create", input)
		r = task.CreateManualTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) CrontabTaskCreate() {
	var input task.CrontabTaskCreateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "crontab_task_create", input)
		r = task.CreateCrontabTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) AutoTaskCreate() {
	var input task.AutoTaskCreateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "auto_task_create", input)
		r = task.CreateAutoTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) ManualTaskUpdate() {
	var input task.ManualTaskUpdateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "manual_task_update", input)
		r = task.UpdateManualTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) CrontabTaskUpdate() {
	var input task.CrontabTaskUpdateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "crontab_task_update", input)
		r = task.UpdateCrontabTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) AutoTaskUpdate() {
	var input task.AutoTaskUpdateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "auto_task_update", input)
		r = task.UpdateAutoTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) TaskStatusUpdate() {
	var input task.TaskStatusUpdateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "task_status_update", input)
		r = task.UpdateTaskStatus(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) TaskDelete() {
	var input task.TaskDeleteInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "task_delete", input)
		r = task.DeleteTask(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) ManualTaskCheck() {
	var input task.ManualTaskCheckInput
	var r ocommon.ResultInfo

	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = task.ManualTaskCheck(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *TaskController) GetMetricValue() {
	var input task.GetMetricValueInput
	var r ocommon.ResultInfo

	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = task.GetMetricValue(input)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

// updateRuleWithCurrentInstanceCount 获取线上实例数并更新规则
func updateRuleWithCurrentInstanceCount(taskId int, ruleInfo *dao.RuleOnline) (r ocommon.ResultInfo) {
	// 获取任务信息
	var taskInfo dao.TaskInfo
	r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, taskId)
	if !r.IsOk() {
		olog.Error("failed to get task info when update rule instance count, taskId:[%d], err:[%v]", taskId, r)
		return
	}

	// 获取模块信息
	var moduleInfo dao.Module
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, taskInfo.ModuleId)
	if !r.IsOk() {
		olog.Error("failed to get module info when update rule instance count, taskId:[%d], err:[%v]", taskId, r)
		return
	}

	// 获取线上实例数
	tagInsList, r := modelComm.GetOnlineIns(moduleInfo.ServiceName, taskInfo.TaskModuleType, taskInfo.IdcTag)
	if !r.IsOk() {
		olog.Error("failed to get online instance count when update rule, taskId:[%d], err:[%v]", taskId, r)
		return
	}

	if len(tagInsList) == 0 {
		olog.Error("no online instance found when update rule, taskId:[%d]", taskId)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "no online instance found", nil, nil)
		return
	}

	currentTagIns := tagInsList[0]

	// 更新规则中的实例数
	updateRuleData := &dao.RuleOnline{
		CurrentNum:     currentTagIns.TotalNum,
		AvailNum:       currentTagIns.AvailNum,
		LastModifyTime: time.Now(),
	}
	updateRuleFields := []string{"CurrentNum", "AvailNum", "LastModifyTime"}

	_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateRuleData, updateRuleFields, ruleInfo.ID)
	if !r.IsOk() {
		olog.Error("failed to update rule instance count, taskId:[%d], ruleId:[%d], err:[%v]", taskId, ruleInfo.ID, r)
		return
	}

	// 更新传入的 ruleInfo 对象，确保后续使用的是最新数据
	ruleInfo.CurrentNum = currentTagIns.TotalNum
	ruleInfo.AvailNum = currentTagIns.AvailNum
	ruleInfo.LastModifyTime = time.Now()

	olog.Info("rule instance count updated successfully, taskId:[%d], ruleId:[%d], currentNum:[%d], availNum:[%d]",
		taskId, ruleInfo.ID, currentTagIns.TotalNum, currentTagIns.AvailNum)

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}
