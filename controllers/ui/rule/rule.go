package rule

import (
	"dxm/siod_sre/auto-scaler/models/task"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/otool"
	modelComm "dxm/siod_sre/auto-scaler/models/comm"

	"github.com/astaxie/beego"
)

type RuleController struct {
	beego.Controller
}

func (c *RuleController) GetRuleModel() {
	var input task.RuleModelQuery
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = task.GetRuleModel(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJ<PERSON>N()
}

func (c *RuleController) CreateRuleModel() {
	var input task.RuleModelCreateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.<PERSON>() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.<PERSON>() {
		modelComm.OperateRecordUser(username, "rule_model_create", input)
		r = task.CreateRuleModel(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *RuleController) ModifyRuleModel() {
	var input task.RuleModelModifyInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
	}
	if r.IsOk() {
		modelComm.OperateRecordUser(username, "rule_model_modify", input)
		r = task.ModifyRuleModel(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *RuleController) DeleteRuleModel() {
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		ruleId, err := c.GetInt("ruleId")
		if err != nil {
			r = ocommon.GenResultInfo(400, "invalid ruleId parameter", nil, nil)
		} else {
			modelComm.OperateRecordUser(username, "rule_model_delete", map[string]interface{}{"ruleId": ruleId})
			r = task.DeleteRuleModel(ruleId)
		}
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}
