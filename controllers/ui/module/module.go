package module

import (
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	modelComm "dxm/siod_sre/auto-scaler/models/comm"
	"dxm/siod_sre/auto-scaler/models/module"

	"dxm/siod-cloud/go-common-lib/ocommon"

	"github.com/astaxie/beego"
)

type ModuleController struct {
	beego.Controller
}

type moduleListInput struct {
	module.ModuleListInput
}

type moduleCreateInput struct {
	module.ModuleCreateInput
}

type moduleDeleteInput struct {
	module.ModuleDeleteInput
}

type disableAutoTaskInput struct {
	module.DisableAutoTaskInput
}

// 获取模型列表
func (c *ModuleController) GetModuleNameList() {

	type moduleListInput struct {
		Name string `json:"name"`
		Type string `json:"type"`
	}

	var r ocommon.ResultInfo
	var input moduleListInput
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.<PERSON>() {
		r = module.GetModuleNameList(input.Name, input.Type)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

// 获取伸缩组列表
func (c *ModuleController) GetScalableGroupList() {
	var input moduleListInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input.ModuleListInput, &c.Controller)
	if r.IsOk() {
		var in bool
		in, r = otool.IsInArray(input.ModuleListInput.ModuleType, []string{"POD", "RISK_MODEL"})
		if !r.IsOk() || !in {
			olog.Error("module type invailed when get scalable group list, type: %s, err:[%v]", input.ModuleListInput.ModuleType, r)
		}
	}

	if r.IsOk() {
		r = module.GetScalableGroupList(input.ModuleListInput)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

// 创建伸缩组
func (c *ModuleController) CreateScalableGroup() {
	var input moduleCreateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input.ModuleCreateInput, &c.Controller)
		if !r.IsOk() {
			olog.Error("failed to parse input params when create scalable group, err:[%v]", r)
		}
	}

	if r.IsOk() {
		modelComm.OperateRecordUser(username, "scalable_group_create", input)
		r = module.CreateScalableGroup(input.ModuleCreateInput)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

// 更新伸缩组
func (c *ModuleController) ModifyScalableGroup() {
	var input moduleCreateInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input.ModuleCreateInput, &c.Controller)
		if !r.IsOk() {
			olog.Error("failed to parse input params when update scalable group, err:[%v]", r)
		}
	}

	if r.IsOk() {
		modelComm.OperateRecordUser(username, "scalable_group_modify", input)
		r = module.ModifyScalableGroup(input.ModuleCreateInput)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

// 删除伸缩组
func (c *ModuleController) DeleteScalableGroup() {
	var input moduleDeleteInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input.ModuleDeleteInput, &c.Controller)
		if !r.IsOk() {
			olog.Error("failed to parse input params when delete scalable group, err:[%v]", r)
		}
	}

	if r.IsOk() {
		modelComm.OperateRecordUser(username, "scalable_group_delete", input)
		r = module.DeleteScalableGroup(input.ModuleDeleteInput)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

// 禁止自动任务开关
func (c *ModuleController) DisableAutoTask() {
	var input module.DisableAutoTaskInput
	var r ocommon.ResultInfo
	username := c.Ctx.Input.GetData("username").(string)
	r = modelComm.CheckUserAuthRole(username)
	if r.IsOk() {
		r = otool.ParseRequestParams(&input, &c.Controller)
		if !r.IsOk() {
			olog.Error("failed to parse input params when disable auto task, err:[%v]", r)
		}
	}

	if r.IsOk() {
		modelComm.OperateRecordUser(username, "disable_auto_task", input)
		r = module.DisableAutoTask(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *ModuleController) GetOnlineIns() {
	var (
		r           ocommon.ResultInfo
		serviceName = c.GetString("serviceName")
		moduleType  = c.GetString("moduleType")
		idcTag      = c.GetString("idcTag")
	)

	if serviceName == "" {
		r = ocommon.GenResultInfo(oerrno.ERR_PARAMS_INVALID, "serviceName is empty", nil, nil)
	}

	if find, _ := otool.IsInArray(moduleType, []string{"POD", "RISK_MODEL"}); !find {
		r = ocommon.GenResultInfo(oerrno.ERR_PARAMS_INVALID, "moduleType invailed", nil, nil)
	}

	if r.IsOk() {
		r = module.GetOnlineIns(serviceName, moduleType, idcTag)
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}
