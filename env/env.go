/*
 *    @package:env
 *    @author:<EMAIL>
 *    @Modifier:
 *    @usage:初始化模块，初始化各子模块或公共库中功能模块
 *    @date: 2024-03-15 16:34
 *    @Last modified: 2024-03-15 16:34
 */
package env

import (
	"fmt"
	"strings"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/plugins/cors"
	_ "github.com/go-sql-driver/mysql" //!使用数据库时必须调用

	Init "dxm/siod-cloud/go-common-lib/init"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/olog"
	auth_center "dxm/siod-cloud/go-common-lib/user-center/auth-center"
	flow_center "dxm/siod-cloud/go-common-lib/user-center/flow-center"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/service_capacity"
	"dxm/siod_sre/auto-scaler/pkg/service_model"
)

var first = true

func InitAllModels() {
	if first == true {
		first = false
	} else {
		return
	}
	beego.Notice("runmode[conf]", beego.AppConfig.String("runmode"))
	beego.Notice("runmode[BConfig]", beego.BConfig.RunMode)

	//!初始化错误码
	ocommon.Init(oerrno.ErrnoMap, errno.ErrnoMap)

	//!log
	Init.InitL4g()
	RegisterAccessLog()
	olog.Init()
	//!数据库初始化
	dao.InitDB()
	Init.InitDB()
	dao.InitOceanDB()
	dao.InitDB2()

	flow_center.InitEnv()
	auth_center.InitEnv()
	global.InitEnv()
	service_capacity.Init()
	if global.YunEnv == "yun_env_xd" {
		service_model.Init()
	}

	// 初始化用户角色
	//InitDefaultRolePermission()
	//InitSuperUserRole()

	// 允许跨域请求
	beego.InsertFilter("*", beego.BeforeRouter, cors.Allow(&cors.Options{
		//AllowAllOrigins: true
		AllowOrigins:     []string{"http://sa.duxiaoman-int.com", "http://sc.duxiaoman-int.com", "http://ops.baidu-int.com"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Authorization", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Content-Type"},
		ExposeHeaders:    []string{"Content-Length", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Content-Type"},
		AllowCredentials: true,
	}))
	beego.InsertFilter("/auto-scaler/ui/*", beego.BeforeRouter, auth_center.AuthFilterV2)
}

func RegisterRouterFilter() {
	//!使用product token认证
	//    //!pressure提交
	//    ofilter.RegisterRouterFilter("/*")

	//    //!start session switch
	//    beego.BConfig.WebConfig.Session.SessionOn = true
}

func RegisterAccessLog() {
	strAccsessRouters := beego.AppConfig.String("accessRouters")
	accsessRouters := strings.Split(strAccsessRouters, ";")
	fmt.Printf("accessLog routers=%+v\n", accsessRouters)
	for _, one := range accsessRouters {
		one = strings.TrimSpace(one)
		if len(one) > 0 {
			olog.RegisterAccessLogFilter(one)
		}
	}
}
