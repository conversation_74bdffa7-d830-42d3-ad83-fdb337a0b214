package service_model

import (
	"dxm/siod-cloud/go-common-lib/olog"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/astaxie/beego"
)

var (
	ServerBns string
)

func Init() {
	ServerBns = beego.AppConfig.String("model_server_bns")
	if ServerBns == "" {
		olog.Error("failed to init model service, ServerBns is empty")
		fmt.Println("failed to init model service, ServerBns is empty")
		os.Exit(1)
	}
}

func splitModelName(modelName string) (appId, stateId int, err error) {
	// 获取模型信息
	arr := strings.Split(modelName, "-")
	if len(arr) < 2 {
		return 0, 0, fmt.E<PERSON><PERSON>("failed to split app_id and state_id from bns name, name:%s", modelName)
	}

	appId, err1 := strconv.Atoi(arr[0])
	stateId, err2 := strconv.Atoi(arr[1])
	if err1 != nil || err2 != nil {
		return 0, 0, fmt.Errorf("failed to parse app_id or state_id from bns name, err:%v, err:%v", err1, err2)
	}
	return
}
