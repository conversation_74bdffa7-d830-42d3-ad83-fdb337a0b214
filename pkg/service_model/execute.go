package service_model

import (
	"dxm/siod-cloud/go-common-lib/ointeraction"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"dxm/siod_sre/auto-scaler/pkg/httpx"
	"encoding/json"
	"fmt"
)

type ModelEngineRes struct {
	ResCode   string `json:"res_code"`
	ResMsg    string `json:"res_msg"`
	Timestamp int    `json:"timestamp"`
	Data      struct {
		TaskId int `json:"task_id"`
	} `json:"data"`
}

type ModelInput struct {
	AppId   string `json:"app_id"`
	StateId string `json:"state_id"`
	Version string `json:"version"`
	FsPath  string `json:"fs_path"`
	Type    string `json:"type" decription:"模型类型, 扩容:up,缩容:down"`
	Num     int    `json:"num" decription:"预期数量"`
}

func ModelEngineExecute(serviceName, execType string, expectNum int) (request ModelInput, response ModelEngineRes, err error) {
	appId, stateId, err := splitModelName(serviceName)
	if err != nil {
		err = fmt.Errorf("failed to split service name, err: %v", err)
		return
	}
	modelInfo, err := GetModelInfo(serviceName)
	if err != nil || len(modelInfo.ResCode) > 0 {
		err = fmt.Errorf("failed to get model info, err: %v, res_code: %s, res_msg: %s", err, modelInfo.ResCode, modelInfo.ResMsg)
		return
	}

	if len(modelInfo.Data.Instances) == 0 {
		err = fmt.Errorf("failed to get model version, service_name: %s", serviceName)
		return
	}

	var m = ModelInput{
		AppId:   fmt.Sprintf("%d", appId),
		StateId: fmt.Sprintf("%d", stateId),
		Version: modelInfo.Data.Instances[0].Version,
		FsPath:  "-",
		Type:    execType,
		Num:     expectNum,
	}

	postJson, _ := json.Marshal(&m)
	url := fmt.Sprintf("http://%s/api/scale_model", apptree.GetServicePort(ServerBns))
	// url := fmt.Sprintf("http://%s/api/scale_model", "10.72.78.123:8900") // 测试环境
	resp, r := httpx.SendHttpBodyRequest(url, ointeraction.HTTP_METHOD_POST_BODY_BY_CONTENT, postJson)
	if !r.IsOk() {
		err = fmt.Errorf("failed to send http request to model engine, url: %s, err: %v", url, r)
		return
	}

	err = json.Unmarshal([]byte(resp), &response)
	if err != nil {
		err = fmt.Errorf("failed to unmarshal response from model engine, err: %v", err)
		return
	}
	return
}
