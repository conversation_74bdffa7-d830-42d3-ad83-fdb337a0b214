package service_model

import (
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"dxm/siod_sre/auto-scaler/pkg/httpx"
	"encoding/json"
	"errors"
	"fmt"
)

type RiskModelInfoData struct {
	ResCode string `json:"res_code" description:"返回码"`
	ResMsg  string `json:"res_msg" description:"返回信息"`
	Data    struct {
		Instances []RiskModelInfoIns `json:"instances"`
	} `json:"data"`
}

type RiskModelInfoIns struct {
	AppId   string  `json:"app_id" description:"应用ID"`
	StateId string  `json:"state_id"`
	Host    string  `json:"host" description:"主机"`
	IdcTag  string  `json:"pool" description:"机房"`
	IsShare int     `json:"isShare" description:"是否共享模型"`
	CpuCore float64 `json:"cpu_core" description:"cpu核数"`
	Num     int     `json:"num" description:"模型数"`
	Version string  `json:"version" description:"模型版本"`
	Port    string  `json:"port" description:"端口"`
}

func GetModelInfo(modelName string) (res RiskModelInfoData, err error) {
	// 获取模型信息
	appId, stateId, err := splitModelName(modelName)
	if err != nil {
		return
	}

	var response *RiskModelInfoData
	response, err = getRiskModelInfo(appId, stateId)
	if err != nil {
		return
	}

	res = *response
	return
}

// 获取模型属性信息
func getRiskModelInfo(appId, stateId int) (res *RiskModelInfoData, err error) {
	url := fmt.Sprintf("http://%s/api/get_model", apptree.GetServicePort(ServerBns))
	form := map[string]interface{}{
		"app_id":   appId,
		"state_id": stateId,
	}
	responseBody, r := httpx.SendHttpFormRequest(url, "GET", form)
	if !r.IsOk() {
		return nil, errors.New("failed to get risk data from prometheus api")
	}
	var Res RiskModelInfoData
	err = json.Unmarshal([]byte(responseBody), &Res)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body to struct, err:%v", err)
	}
	return
}
