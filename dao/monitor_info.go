package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"fmt"

	"github.com/astaxie/beego/orm"
)

// 监控信息表
const (
	tableNameMonitorInfo = "monitor_info"
	monitorInfoKey       = "id"
)

type MonitorInfo struct {
	ID          int     `orm:"column(id);size(20);auto;pk" json:"id" description:"ID"`
	ModuleID    int     `orm:"column(module_id);size(11)" json:"moduleId" description:"模块ID"`
	ServiceName string  `orm:"column(service_name);size(64)" json:"serviceName" description:"服务名称"`
	LogicIDC    string  `orm:"column(logic_idc);size(32)" json:"logicIdc" description:"逻辑机房"`
	InsCount    int     `orm:"column(ins_count);size(11)" json:"insCount" description:"实例数"`
	CPUAvg      float64 `orm:"column(cpu_avg);size(11)" json:"cpuAvg" description:"CPU平均值"`
	CPUServ     float64 `orm:"column(cpu_serv);size(11)" json:"cpuServ" description:"服务CPU使用率"`
	QPSAvg      float64 `orm:"column(qps_avg);size(11)" json:"qpsAvg" description:"QPS平均值"`
	CostAvg     float64 `orm:"column(cost_avg);size(11)" json:"costAvg" description:"平均耗时"`
	MemAvg      float64 `orm:"column(mem_avg);size(11)" json:"memAvg" description:"内存平均值"`
	Level       float64 `orm:"column(level);size(11)" json:"level" description:"水位"`
	HttpCodeNum int     `orm:"column(http_code_num);size(11)" json:"httpCodeNum" description:"HTTP错误码数量"`
	CollectTime string  `orm:"column(collect_time);size(64)" json:"collectTime" description:"采集时间"`
}

type MonitorInfoBuilder struct {
	dbBase.DbBase
}

func CreateMonitorInfoPtr() *MonitorInfoBuilder {
	return &MonitorInfoBuilder{dbBase.DbBase{
		Tablename:    tableNameMonitorInfo,
		PrimaryKey:   monitorInfoKey,
		PtrDaoStruct: &MonitorInfo{},
	}}
}
func (c *MonitorInfoBuilder) GetLatestBetweenTime(bns, idcTag, start, end string) (detailList []MonitorInfo, err error) {
	o := orm.NewOrm()
	var sql string
	if idcTag == "" {
		sql = fmt.Sprintf("SELECT * FROM(SELECT * FROM `monitor_info` WHERE `service_name`='%s' AND `collect_time` BETWEEN '%s' AND '%s' ORDER BY `collect_time` DESC LIMIT 10)t GROUP BY `logic_idc`", bns, start, end)
	} else {
		sql = fmt.Sprintf("SELECT * FROM(SELECT * FROM `monitor_info` WHERE `service_name`='%s' AND `logic_idc`='%s' AND `collect_time` BETWEEN '%s' AND '%s' ORDER BY `collect_time` DESC LIMIT 10)t GROUP BY `logic_idc`", bns, idcTag, start, end)
	}
	_, err = o.Raw(sql).QueryRows(&detailList)
	return
}
